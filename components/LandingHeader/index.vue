<template>
  <header
    class="landing-header"
    :class="{ 'header-hidden': !showHeader, 'auth-modal-active': showModal, 'settings-modal-active': showSettingsModal }"
    :style="{
      '--header-bg-opacity': headerOpacity,
      '--backdrop-filter': headerOpacity > 0 ? `blur(${headerOpacity * 20}px)` : 'none',
      backgroundColor: isDarkBackground
        ? `rgba(34, 34, 34, ${headerOpacity * 0.8})`
        : `rgba(255, 255, 255, ${headerOpacity * 0.8})`
    }"
  >
    <div class="header-container">
      <!-- 左侧：Logo + 导航链接 -->
      <div class="header-left">
        <!-- Logo -->
        <nuxt-link to="/" class="logo-link">
          <img
            :src="isDarkBackground ? '/image/darklogo2.png' : '/image/newlogo1.png'"
            width="95"
            height="45"
            :alt="isDarkBackground ? 'DINQ dark logo' : 'DINQ logo'"
          />
        </nuxt-link>
        
        <!-- 桌面端导航链接 -->
        <nav class="nav-links desktop-nav" :class="{ 'dark-nav': isDarkBackground }">
          <nuxt-link to="/" class="nav-link active">Home</nuxt-link>
          <div class="nav-dropdown" @mouseenter="showDropdown = true" @mouseleave="showDropdown = false">
            <button class="nav-link dropdown-trigger" @click="toggleDropdown">
              Product
              <svg class="dropdown-arrow" :class="{ 'rotate': showDropdown }" width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <div class="dropdown-menu" :class="{ 'show': showDropdown, 'dark-dropdown': isDarkBackground }">
              <nuxt-link to="/analysis" class="dropdown-item">Analysis</nuxt-link>
              <nuxt-link to="/search" class="dropdown-item">Search</nuxt-link>
              <nuxt-link to="/billboard" class="dropdown-item">Billboard</nuxt-link>
            </div>
          </div>
          <button class="nav-link" @click="showComingSoon('Pricing')">Pricing</button>
          <button class="nav-link" @click="showComingSoon('Blog')">Blog</button>
        </nav>
      </div>
      
      <!-- 桌面端右侧按钮组 -->
      <div class="header-actions desktop-actions">
        <!-- X 按钮 -->
        <a href="https://x.com/dinq_io" target="_blank" class="x-btn">
          <img 
            :src="isDarkBackground ? '/image/header-x-dark.svg' : '/image/header-x.svg'" 
            alt="X" 
            width="22" 
            height="21" 
          />
        </a>
        
        <!-- Request a Demo 按钮 - 只在滚动到第二页时显示，且只在桌面端和平板端 -->
        <transition name="demo-btn-fade" mode="out-in">
          <nuxt-link 
            v-if="showDemoBtn" 
            to="/demo" 
            class="demo-btn" 
            :class="{ 'dark-demo-btn': isDarkBackground }"
          >
            Request a Demo
          </nuxt-link>
        </transition>
        
        <!-- 使用 ClientOnly 包装认证相关组件以避免 SSR hydration 问题 -->
        <ClientOnly>
          <!-- 认证加载状态 -->
          <div v-if="!authInitialized" class="auth-loading">
            <div class="loading-spinner"></div>
          </div>

          <!-- 用户下拉菜单（已登录时显示） -->
          <UserDropdown
            v-else-if="currentUser"
            :open="showUserDropdown"
            :user="{
              photoURL: userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL,
              photoURLs: [userProfile?.profile_picture, firebaseUserProfile?.photo_url, currentUser.photoURL].filter(Boolean),
              name: userProfile?.display_name || firebaseUserProfile?.display_name || currentUser.displayName,
              username: currentUser.username || currentUser.email || '',
              verified: currentUser.verified || true
            }"
            @update:open="showUserDropdown = $event"
            @signout="handleLogout"
            @settings="onSettings"
            @verification="onVerification"
          />

          <!-- Login 按钮（未登录时显示） -->
          <button class="login-btn" :class="{ 'dark-login-btn': isDarkBackground }" @click="showModal = true" v-else>
            Login
          </button>

          <!-- SSR fallback: 显示一个占位符避免布局跳动 -->
          <template #fallback>
            <div class="auth-loading">
              <div class="loading-spinner"></div>
            </div>
          </template>
        </ClientOnly>
      </div>

      <!-- 移动端汉堡菜单按钮 -->
      <button class="mobile-menu-btn" :class="{ 'dark-mobile-btn': isDarkBackground }" @click="toggleMobileMenu">
        <div class="hamburger" :class="{ 'active': showMobileMenu }">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </button>
    </div>

  </header>
  
  <!-- 移动端菜单遮罩（移动到body层） -->
  <Teleport to="body">
    <div 
      v-if="showMobileMenu" 
      class="mobile-menu-overlay" 
      @click="closeMobileMenu"
    ></div>

    <!-- 移动端菜单（移动到body层） -->
    <div class="mobile-menu" :class="{ 'show': showMobileMenu, 'dark-mobile-menu': isDarkBackground }">
      <div class="mobile-menu-content">
        <!-- 用户信息区域 -->
        <div class="mobile-user-section">
          <!-- 使用 ClientOnly 包装移动端认证相关组件 -->
          <ClientOnly>
            <div v-if="currentUser" class="mobile-user-info">
              <div class="mobile-avatar">
                <img
                  v-if="userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL"
                  :src="userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser.photoURL"
                  alt="User avatar"
                  class="mobile-avatar-img"
                />
                <div v-else class="mobile-avatar-placeholder">
                  <div class="i-carbon:user text-2xl text-gray-400"></div>
                </div>
              </div>
              <div class="mobile-user-details">
                <div class="mobile-user-name">
                  {{ userProfile?.display_name || firebaseUserProfile?.display_name || currentUser.displayName || 'User' }}
                </div>
                <div class="mobile-user-email">
                  {{ currentUser.email }}
                </div>
              </div>
            </div>
            <div v-else class="mobile-login-section">
              <button class="mobile-login-btn" @click="showModal = true; closeMobileMenu()">
                Login / Sign Up
              </button>
            </div>

            <!-- SSR fallback: 移动端占位符 -->
            <template #fallback>
              <div class="mobile-login-section">
                <button class="mobile-login-btn" @click="showModal = true; closeMobileMenu()">
                  Login / Sign Up
                </button>
              </div>
            </template>
          </ClientOnly>
        </div>

        <!-- 分割线 -->
        <div class="mobile-menu-divider"></div>

        <!-- 导航菜单 -->
        <nav class="mobile-nav">
          <nuxt-link to="/" class="mobile-nav-item" @click="closeMobileMenu">
            <span>Home</span>
          </nuxt-link>

          <!-- Product 子菜单（展开显示） -->
          <div class="mobile-nav-section">
            <div class="mobile-nav-section-title">Product</div>
            <nuxt-link to="/analysis" class="mobile-nav-item mobile-nav-sub-item" @click="closeMobileMenu">
              <span>Analysis</span>
            </nuxt-link>
            <nuxt-link to="/search" class="mobile-nav-item mobile-nav-sub-item" @click="closeMobileMenu">
              <span>Search</span>
            </nuxt-link>
            <nuxt-link to="/billboard" class="mobile-nav-item mobile-nav-sub-item" @click="closeMobileMenu">
              <span>Billboard</span>
            </nuxt-link>
          </div>

          <button class="mobile-nav-item" @click="showComingSoon('Pricing'); closeMobileMenu()">
            <span>Pricing</span>
          </button>

          <button class="mobile-nav-item" @click="showComingSoon('Blog'); closeMobileMenu()">
            <span>Blog</span>
          </button>
        </nav>

        <!-- 分割线 -->
        <div class="mobile-menu-divider"></div>

        <!-- 底部按钮区域 -->
        <div class="mobile-bottom-actions">
          <!-- X 链接 -->
          <a href="https://x.com/dinq_io" target="_blank" class="mobile-social-btn" @click="closeMobileMenu">
            <span>Follow us on X</span>
          </a>

          <!-- Request a Demo 按钮 -->
          <nuxt-link to="/demo" class="mobile-demo-btn" @click="closeMobileMenu">
            Request a Demo
          </nuxt-link>

          <!-- 登出按钮（已登录时显示） -->
          <button v-if="currentUser" class="mobile-logout-btn" @click="handleMobileLogout">
            <span>Sign Out</span>
          </button>
        </div>
      </div>
    </div>
  </Teleport>
  
  <!-- 认证模态框 -->
  <AuthModal
    ref="authModal"
    :visible="showModal"
    :loading="loading"
    :provider="currentProvider as 'google' | 'github' | 'twitter'"
    @update:visible="showModal = $event"
    @auth="onAuth"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import type { ComponentPublicInstance } from 'vue'
import { getCurrentUser, getCurrentFirebaseUser, updateUserInfo } from '@/api/user'
import UserDropdown from '../UserDropdown.vue'
import AuthModal from '../AuthModal/index.vue'

// 导航栏显示/隐藏控制
const showHeader = ref(true)
// 下拉菜单显示/隐藏控制
const showDropdown = ref(false)
// 用户下拉菜单显示/隐藏控制
const showUserDropdown = ref(false)
// 移动端菜单显示/隐藏控制
const showMobileMenu = ref(false)
// 深色背景section检测（当在深色背景section时为true，用于调整导航栏可见性）
const isDarkBackground = ref(false)
// Request a Demo按钮显示控制（只在第二页及以后显示，且只在桌面端和平板端）
const showDemoBtn = ref(false)

// 认证相关状态
const { $emitter } = useNuxtApp()
const { loading, currentUser, authInitialized, loginWithGithub, loginWithGoogle, logout } = useFirebaseAuth()
const showModal = ref(false)
const authModal = ref<ComponentPublicInstance<{ resetLoading: () => void }> | null>(null)
const currentProvider = ref('')
const router = useRouter()

// 设置浮窗状态
const showSettingsModal = ref(false)

// 用户数据状态
const userProfile = ref<any>(null)
const firebaseUserProfile = ref<any>(null)

// 移动端菜单控制函数
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  // 防止背景滚动
  if (showMobileMenu.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
  document.body.style.overflow = ''
}

// 获取用户详细信息
const fetchUserProfile = async () => {
  if (!currentUser.value?.uid) return
  
  try {
    const [userRes, firebaseRes] = await Promise.all([
      getCurrentUser({ Userid: currentUser.value.uid }),
      getCurrentFirebaseUser({ Userid: currentUser.value.uid })
    ])

    if (userRes.success) {
      userProfile.value = userRes.user
    }
    if (firebaseRes.success) {
      firebaseUserProfile.value = firebaseRes.firebase_user
    }

    // 自动同步 Firebase 的 display_name 和 email 到用户信息（仅在为 null 时）
    if (userRes.success && firebaseRes.success) {
      const updateData = {}
      
      // 检查 display_name
      if ((!userProfile.value?.display_name || userProfile.value.display_name === null) &&
          firebaseUserProfile.value?.display_name) {
        updateData.display_name = firebaseUserProfile.value.display_name
      }
      
      // 检查 email
      if ((!userProfile.value?.email || userProfile.value.email === null) &&
          firebaseUserProfile.value?.email) {
        updateData.email = firebaseUserProfile.value.email
      }
      
      // 如果有需要更新的字段，则执行更新
      if (Object.keys(updateData).length > 0) {
        try {
          const updateRes = await updateUserInfo(updateData, {
            Userid: currentUser.value.uid
          })
          
          if (updateRes.success) {
            // 更新本地状态
            if (updateData.display_name) {
              userProfile.value.display_name = updateData.display_name
              console.log('Auto-synced display_name from Firebase:', updateData.display_name)
            }
            if (updateData.email) {
              userProfile.value.email = updateData.email
              console.log('Auto-synced email from Firebase:', updateData.email)
            }
          }
        } catch (error) {
          console.error('Error auto-syncing user info:', error)
        }
      }
    }
  } catch (error) {
    console.error('Error fetching user profile:', error)
  }
}

// 监听用户登录状态变化
watch(() => currentUser.value?.uid, (newUid) => {
  if (newUid) {
    fetchUserProfile()
  } else {
    userProfile.value = null
    firebaseUserProfile.value = null
  }
}, { immediate: true })

// 认证处理函数
const onAuth = async (provider: 'google' | 'github' | 'twitter') => {
  currentProvider.value = provider
  switch (provider) {
    case 'google':
      await loginWithGoogle()
      break
    case 'github':
      await loginWithGithub()
      break
    case 'twitter':
      break
    default:
      break
  }
  if (currentUser.value) {
    showModal.value = false
  }
  authModal.value?.resetLoading()
}

// 登出处理函数
const handleLogout = () => {
  logout()
  router.push('/')
}

// 移动端登出处理函数
const handleMobileLogout = () => {
  closeMobileMenu()
  handleLogout()
}

// 下拉菜单切换函数
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// Coming Soon 提示函数
const showComingSoon = (feature: string) => {
  // 创建一个简洁的提示
  const toast = document.createElement('div')
  toast.textContent = `${feature} - Coming Soon!`
  toast.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-family: 'Alexandria', sans-serif;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    animation: fadeInOut 2s ease-in-out;
  `
  
  // 添加CSS动画
  if (!document.querySelector('#coming-soon-style')) {
    const style = document.createElement('style')
    style.id = 'coming-soon-style'
    style.textContent = `
      @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
        20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
      }
    `
    document.head.appendChild(style)
  }
  
  document.body.appendChild(toast)
  
  // 2秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 2000)
}

// 设置和认证处理函数
function onSettings() {
  // TODO: 跳转到设置页或弹窗
}

function onVerification() {
  // TODO: 跳转到认证页或弹窗
}

// 滚动监听变量
let lastScrollTop = 0
let scrollTimeout: NodeJS.Timeout | null = null
let landingPageElement: HTMLElement | null = null
let windowScrollHandler: (() => void) | null = null

// 新增：导航栏背景透明度状态
const headerOpacity = ref(0) // 0 = 完全透明，1 = 完全不透明

// 检测当前是否在深色背景section的函数，同时检测是否显示Demo按钮
const checkDarkBackground = () => {
  const sections = document.querySelectorAll('.landing-section')
  const viewportHeight = window.innerHeight
  const headerHeight = viewportHeight * 0.1 // 导航栏高度（10vh）
  
  // 检测是否为移动端（768px以下）
  const isMobile = window.innerWidth <= 768
  
  sections.forEach((section, index) => {
    const rect = section.getBoundingClientRect()
    // 检查section是否与导航栏区域重叠
    if (rect.top <= headerHeight && rect.bottom >= headerHeight) {
      // section索引：0=hero, 1=features, 2=feature-1(深色), 3=feature-2(浅色), 4=feature-3(深色)
      const isDark = index === 2 || index === 4 // 第三页和第五页
      const shouldShowDemo = !isMobile && index >= 1
      
      // 只有在值实际发生变化时才更新状态，避免不必要的重渲染
      if (isDarkBackground.value !== isDark) {
        isDarkBackground.value = isDark
      }
      if (showDemoBtn.value !== shouldShowDemo) {
        showDemoBtn.value = shouldShowDemo
      }
    }
  })
}

// 智能滚动监听函数
const handleScroll = (event: Event) => {
  const scrollContainer = event.target as HTMLElement
  const currentScrollTop = scrollContainer.scrollTop
  const scrollDelta = currentScrollTop - lastScrollTop

  // 清除之前的延时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  // 检测当前背景色
  checkDarkBackground()

  // 计算导航栏背景透明度（基于滚动位置）
  // 在顶部50px内完全透明，50-150px之间逐渐变不透明
  const fadeStartPosition = 50
  const fadeEndPosition = 150

  if (currentScrollTop <= fadeStartPosition) {
    headerOpacity.value = 0 // 完全透明
  } else if (currentScrollTop >= fadeEndPosition) {
    headerOpacity.value = 1 // 完全不透明
  } else {
    // 在过渡区间内计算透明度
    const progress = (currentScrollTop - fadeStartPosition) / (fadeEndPosition - fadeStartPosition)
    headerOpacity.value = progress
  }

  // 滚动阈值（避免微小滚动触发）
  const scrollThreshold = 8

  // 在页面顶部始终显示导航栏
  if (currentScrollTop <= 100) {
    showHeader.value = true
  } else if (Math.abs(scrollDelta) > scrollThreshold) {
    // 向下滚动隐藏，向上滚动显示
    if (scrollDelta > 0) {
      // 向下滚动 - 隐藏导航栏
      showHeader.value = false
    } else {
      // 向上滚动 - 显示导航栏
      showHeader.value = true
    }
  }

  // 设置延时器，停止滚动后自动显示导航栏（用户体验优化）
  scrollTimeout = setTimeout(() => {
    // 只有在不是页面顶部时才自动显示
    if (currentScrollTop > 100) {
      showHeader.value = true
    }
  }, 2000) // 2秒后自动显示导航栏

  lastScrollTop = currentScrollTop
}

// 窗口resize处理函数
const handleResize = () => {
  checkDarkBackground()
}

// 生命周期钩子
onMounted(() => {
  // 等待DOM渲染完成后获取滚动容器
  nextTick(() => {
    // 首先尝试找到 .landing-page 容器
    landingPageElement = document.querySelector('.landing-page')
    if (landingPageElement) {
      landingPageElement.addEventListener('scroll', handleScroll, { passive: true })
      console.log('✅ Added scroll listener to .landing-page')
    } else {
      // 备用方案：监听window滚动
      windowScrollHandler = () => {
        handleScroll({ target: { scrollTop: window.scrollY } } as any)
      }
      window.addEventListener('scroll', windowScrollHandler, { passive: true })
      console.log('⚠️ Using fallback: listen to window scroll')
    }
    
    // 添加窗口resize监听
    window.addEventListener('resize', handleResize, { passive: true })
    
    // 初始检查背景色状态和Demo按钮显示
    setTimeout(() => {
      checkDarkBackground()
      // 初始化导航栏透明度
      const initialScrollTop = landingPageElement ? landingPageElement.scrollTop : window.scrollY
      const fadeStartPosition = 50
      const fadeEndPosition = 150

      if (initialScrollTop <= fadeStartPosition) {
        headerOpacity.value = 0
      } else if (initialScrollTop >= fadeEndPosition) {
        headerOpacity.value = 1
      } else {
        const progress = (initialScrollTop - fadeStartPosition) / (fadeEndPosition - fadeStartPosition)
        headerOpacity.value = progress
      }
    }, 100)
  })
  
  // 监听认证事件
  $emitter.on('auth', () => {
    showModal.value = true
  })
  // 监听用户信息更新事件
  $emitter.on('user-profile-updated', () => {
    fetchUserProfile()
  })
  // 监听设置浮窗事件
  $emitter.on('settings-modal', (isOpen) => {
    showSettingsModal.value = isOpen
  })
})

onUnmounted(() => {
  // 移除滚动监听
  if (landingPageElement) {
    landingPageElement.removeEventListener('scroll', handleScroll)
  } else if (windowScrollHandler) {
    window.removeEventListener('scroll', windowScrollHandler)
  }
  
  // 移除resize监听
  window.removeEventListener('resize', handleResize)
  
  // 清理定时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  
  // 关闭移动端菜单并恢复滚动
  if (showMobileMenu.value) {
    document.body.style.overflow = ''
  }
  
  $emitter.off('auth', () => {})
  $emitter.off('user-profile-updated', () => {})
  $emitter.off('settings-modal', () => {})
})
</script>

<style scoped>
.landing-header {
  position: fixed;
  top: 0; /* 贴顶部，消除缝隙 */
  left: 0;
  right: 0;
  width: 100%; /* 导航栏保持全宽，横跨整个屏幕 */
  z-index: 1000;
  /* 移除固定的透明背景，改为动态控制 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.2s ease,
              backdrop-filter 0.2s ease;
  transform: translateY(0);
  opacity: 1;
  /* 添加毛玻璃效果的支持 */
  -webkit-backdrop-filter: var(--backdrop-filter, none);
  backdrop-filter: var(--backdrop-filter, none);
}

.landing-header.auth-modal-active {
  z-index: 40;
}

.landing-header.settings-modal-active {
  z-index: 40;
}

.landing-header.header-hidden {
  transform: translateY(-120%);
  opacity: 0;
  pointer-events: none;
}

.header-container {
  width: 100%;
  /* 移除最大宽度限制，导航栏内容横跨整个屏幕宽度 */
  padding: 0 24px;
  height: 10vh; /* 使用 vh 单位，占视窗高度的 10% */
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box; /* 确保padding包含在宽度内 */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 48px;
}

.logo-link {
  display: flex;
  align-items: center;
  transition: opacity 0.2s ease;
}

.logo-link:hover {
  opacity: 0.8;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-dropdown {
  position: relative;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
}

.dropdown-arrow {
  transition: transform 0.2s ease;
}

.dropdown-arrow.rotate {
  transform: rotate(180deg);
}

.nav-link {
  font-family: 'Alexandria', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  background: transparent;
  border: none;
  cursor: pointer;
}

.nav-link:hover {
  color: #CB7C5D;
}

.nav-link.active {
  font-weight: 700;
  color: #333;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #CB7C5D;
  transition: width 0.2s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* 深色背景时的导航链接样式 */
.dark-nav .nav-link {
  color: #FFFFFF;
}

.dark-nav .nav-link.active {
  color: #FFFFFF;
}

.dark-nav .nav-link:hover {
  color: #CB7C5D;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 160px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
  padding: 0;
  margin-top: 8px;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 12px 20px;
  font-family: 'Alexandria', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #CB7C5D;
}

/* 第一个菜单项的hover样式 - 匹配上圆角 */
.dropdown-item:first-child:hover {
  border-radius: 8px 8px 0 0;
}

/* 最后一个菜单项的hover样式 - 匹配下圆角 */
.dropdown-item:last-child:hover {
  border-radius: 0 0 8px 8px;
}

/* 如果只有一个菜单项，保持完整圆角 */
.dropdown-item:only-child:hover {
  border-radius: 8px;
}

/* 深色下拉菜单样式 */
.dark-dropdown {
  background: #2A2A2A;
  border: 1px solid #404040;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark-dropdown .dropdown-item {
  color: #FFFFFF;
}

.dark-dropdown .dropdown-item:hover {
  background: #404040;
  color: #CB7C5D;
}

/* 深色主题下的圆角处理 */
.dark-dropdown .dropdown-item:first-child:hover {
  border-radius: 8px 8px 0 0;
}

.dark-dropdown .dropdown-item:last-child:hover {
  border-radius: 0 0 8px 8px;
}

.dark-dropdown .dropdown-item:only-child:hover {
  border-radius: 8px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.x-btn {
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.x-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.demo-btn {
  width: 180px;
  height: 40px;
  background: #000000;
  border: 1px solid #000000;
  border-radius: 25px;
  color: white;
  font-family: 'Alexandria', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-right: 16px;
  white-space: nowrap;
}

.demo-btn:hover {
  background: #333333;
  border-color: #333333;
}

/* Request a Demo 按钮手风琴动画 + 淡入淡出 */
.demo-btn-fade-enter-active,
.demo-btn-fade-leave-active {
  transition: width 0.3s linear, margin-right 0.3s linear, opacity 0.3s linear;
  overflow: hidden;
}

.demo-btn-fade-enter-from {
  width: 0;
  margin-right: 0;
  opacity: 0;
}

.demo-btn-fade-leave-to {
  width: 0;
  margin-right: 0;
  opacity: 0;
}

.demo-btn-fade-enter-to,
.demo-btn-fade-leave-from {
  width: 180px;
  margin-right: 16px;
  opacity: 1;
}

.login-btn {
  padding: 10px 20px;
  background: transparent;
  border: none;
  border-radius: 25px;
  color: #000000;
  font-family: 'Alexandria', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #000000;
}

/* 深色背景时的按钮样式 */
.dark-demo-btn {
  background: #FFFFFF;
  border-color: #FFFFFF;
  color: #000000;
}

.dark-demo-btn:hover {
  background: #F0F0F0;
  border-color: #F0F0F0;
  color: #000000;
}

.dark-login-btn {
  background: transparent;
  color: #FFFFFF;
}

.dark-login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

/* 移动端汉堡菜单按钮 */
.mobile-menu-btn {
  display: none;
  width: 44px;
  height: 44px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.mobile-menu-btn:hover .hamburger span {
  background: #CB7C5D;
}

.dark-mobile-btn:hover .hamburger span {
  background: #CB7C5D;
}

/* 汉堡菜单图标 */
.hamburger {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 24px;
  height: 20px;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: #333;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.dark-mobile-btn .hamburger span {
  background: #FFFFFF;
}

/* 汉堡菜单激活状态 */
.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(8px, -7px);
}

/* 移动端菜单遮罩 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* 移动端菜单 */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 300px; /* 调整为更合适的宽度 */
  max-width: 85vw; /* 防止超出屏幕，最大85%视窗宽度 */
  height: 100vh;
  background: #FFFFFF;
  z-index: 9999;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  overflow-x: hidden; /* 防止水平滚动 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.mobile-menu.show {
  right: 0;
}

.dark-mobile-menu {
  background: #1F1F22;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
}

.mobile-menu-content {
  height: 100%;
  overflow-y: auto;
  padding: 24px 0;
  display: flex;
  flex-direction: column;
}

/* 移动端用户信息区域 */
.mobile-user-section {
  padding: 0 24px 0 24px;
  margin-bottom: 8px;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.mobile-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.dark-mobile-menu .mobile-avatar-placeholder {
  background: #374151;
}

.mobile-user-details {
  flex: 1;
  min-width: 0;
}

.mobile-user-name {
  font-family: 'Alexandria', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark-mobile-menu .mobile-user-name {
  color: #F9FAFB;
}

.mobile-user-email {
  font-family: 'Alexandria', sans-serif;
  font-size: 14px;
  color: #6B7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark-mobile-menu .mobile-user-email {
  color: #9CA3AF;
}

.mobile-login-section {
  text-align: center;
}

.mobile-login-btn {
  width: 100%;
  padding: 12px 20px;
  background: #000000;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-family: 'Alexandria', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-login-btn:hover {
  background: #333333;
}

.dark-mobile-menu .mobile-login-btn {
  background: #FFFFFF;
  color: #000000;
}

.dark-mobile-menu .mobile-login-btn:hover {
  background: #F3F4F6;
}

/* 移动端菜单分割线 */
.mobile-menu-divider {
  height: 1px;
  background: #E5E7EB;
  margin: 20px 24px;
}

.dark-mobile-menu .mobile-menu-divider {
  background: #374151;
}

/* 移动端导航 */
.mobile-nav {
  flex: 1;
  padding: 0 24px;
}

.mobile-nav-section {
  margin-bottom: 16px;
}

.mobile-nav-section-title {
  font-family: 'Alexandria', sans-serif;
  font-size: 12px;
  font-weight: 700;
  color: #9CA3AF;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 12px;
}

.dark-mobile-menu .mobile-nav-section-title {
  color: #6B7280;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  padding: 14px 12px;
  border-radius: 8px;
  color: #374151;
  text-decoration: none;
  font-family: 'Alexandria', sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  width: 100%;
  cursor: pointer;
  margin-bottom: 4px;
}

.mobile-nav-item:hover {
  background: #F3F4F6;
  color: #CB7C5D;
}

.dark-mobile-menu .mobile-nav-item {
  color: #E5E7EB;
}

.dark-mobile-menu .mobile-nav-item:hover {
  background: #374151;
  color: #CB7C5D;
}

.mobile-nav-sub-item {
  padding-left: 24px;
  font-size: 15px;
  font-weight: 400;
  color: #6B7280;
}

.dark-mobile-menu .mobile-nav-sub-item {
  color: #9CA3AF;
}



/* 移动端底部按钮区域 */
.mobile-bottom-actions {
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: transparent;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  color: #374151;
  text-decoration: none;
  font-family: 'Alexandria', sans-serif;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.mobile-social-btn:hover {
  background: #F9FAFB;
  border-color: #CB7C5D;
  color: #CB7C5D;
}

.dark-mobile-menu .mobile-social-btn {
  border-color: #4B5563;
  color: #E5E7EB;
}

.dark-mobile-menu .mobile-social-btn:hover {
  background: #374151;
  border-color: #CB7C5D;
  color: #CB7C5D;
}

.mobile-demo-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 20px;
  background: #000000;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-family: 'Alexandria', sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.mobile-demo-btn:hover {
  background: #333333;
}

.dark-mobile-menu .mobile-demo-btn {
  background: #FFFFFF;
  color: #000000;
}

.dark-mobile-menu .mobile-demo-btn:hover {
  background: #F3F4F6;
}

.mobile-logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: transparent;
  border: 1px solid #FCA5A5;
  border-radius: 8px;
  color: #DC2626;
  font-family: 'Alexandria', sans-serif;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-logout-btn:hover {
  background: #FEF2F2;
  border-color: #DC2626;
}

.dark-mobile-menu .mobile-logout-btn {
  border-color: #DC2626;
  color: #DC2626;
}

.dark-mobile-menu .mobile-logout-btn:hover {
  background: #450A0A;
  border-color: #DC2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .landing-header {
    top: 0; /* 移动端贴顶部，消除空隙 */
    /* 确保移动端也支持毛玻璃效果 */
    -webkit-backdrop-filter: var(--backdrop-filter, none);
    backdrop-filter: var(--backdrop-filter, none);
  }
  
  .header-container {
    padding: 0 20px; /* 统一使用20px padding */
    height: 8vh; /* 移动端稍微减小导航栏高度 */
    max-width: 100%; /* 移动端使用100%宽度 */
  }
  
  .header-left {
    gap: 20px; /* 减少间距以节省空间 */
  }
  
  /* 移动端隐藏桌面端导航 */
  .desktop-nav {
    display: none;
  }
  
  .desktop-actions {
    display: none;
  }
  
  /* 移动端显示汉堡菜单 */
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-dropdown {
    position: static;
  }
  
  .dropdown-menu {
    position: fixed;
    left: 50%;
    transform: translateX(-50%) translateY(-10px);
    min-width: 200px;
  }
  
  .dropdown-menu.show {
    transform: translateX(-50%) translateY(0);
  }
}

/* 平板端调整 */
@media (max-width: 1024px) and (min-width: 769px) {
  .header-container {
    padding: 0 24px; /* 平板端保持24px padding */
    /* 移除最大宽度限制 */
  }
  
  .header-left {
    gap: 32px;
  }
  
  .nav-links {
    gap: 24px;
  }
  
  .header-actions {
    gap: 12px;
  }
  
  .demo-btn {
    width: 160px;
    font-size: 13px;
  }
}

/* 小屏幕移动端优化 */
@media (max-width: 480px) {
  .header-container {
    padding: 0 16px; /* 小屏幕进一步减少padding */
  }
  
  .header-left {
    gap: 16px;
  }
}

/* 认证加载状态样式 */
.auth-loading {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  min-width: 80px;
  justify-content: center;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #666;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dark .loading-spinner {
  border-color: #666;
  border-top-color: #ccc;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 