<template>
  <div class="relative" ref="dropdownRef" @mouseenter="handleAvatarHover" @mouseleave="handleAvatarLeave">
    <div class="w-[42px] h-[42px] rounded-full overflow-hidden flex items-center justify-center cursor-pointer avatar-container" @click="toggleDropdown">
      <img v-if="currentAvatarUrl" :src="currentAvatarUrl" alt="User avatar" class="w-[42px] h-[42px] rounded-full object-cover" @error="handleAvatarError" />
      <div v-else class="i-carbon:user text-2xl text-gray-400"></div>
    </div>
    <transition name="fade">
      <div v-if="props.open" class="dropdown-menu" style="top: 68px;" @mouseenter="handleDropdownEnter" @mouseleave="handleDropdownLeave">
        <div class="flex items-center gap-3 mb-4 w-full user-dropdown-header">
          <img v-if="currentAvatarUrl" :src="currentAvatarUrl" alt="User avatar" class="w-12 h-12 rounded-full object-cover" @error="handleAvatarError" />
          <div v-else class="i-carbon:user text-3xl text-gray-400 w-12 h-12 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"></div>
          <div class="flex-1">
            <div class="font-semibold text-base text-gray-900 dark:text-white flex items-center gap-1">
              <span class="user-name">{{ user?.name || '--' }}</span>
              <span v-if="user?.verified" class="i-carbon:checkmark-filled text-primary-500 ml-1"></span>
            </div>
            <div class="text-xs text-gray-400 user-username">{{ user?.username || '--' }}</div>
          </div>
        </div>
        <div>
          <button class="dropdown-btn" @click="openSettingsModal('verification')">
            <img src="/image/verification.png" class="w-4 h-4" alt="Verification icon" />
            <span class="text-gray-900 dark:text-white text-sm" style="color: #8D8D8D;">Verification</span>
          </button>
          <div class="dropdown-separator"></div>
          <button class="dropdown-btn" @click="openSettingsModal('settings')">
            <img src="/image/settings.png" class="w-4 h-4" alt="Settings icon" />
            <span class="text-gray-900 dark:text-white text-sm" style="color: #8D8D8D;">Settings</span>
          </button>
          <div class="dropdown-separator"></div>
          <button class="dropdown-btn" @click="handleSignout">
            <img src="/image/signout.png" class="w-4 h-4" alt="Sign out icon" />
            <span class="text-gray-900 dark:text-white text-sm" style="color: #8D8D8D;">Sign out</span>
          </button>
        </div>
      </div>
    </transition>
    <Teleport to="body">
      <SettingsModal v-if="showSettingsModal" :user="user" :tab="activeTab" @close="closeSettingsModal" @signout="handleSignout" />
    </Teleport>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import SettingsModal from './SettingsModal/index.vue'

const { $emitter } = useNuxtApp()

const props = defineProps({
  user: {
    type: Object,
    default: () => ({})
  },
  open: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['signout', 'update:open'])

const showSettingsModal = ref(false)
const activeTab = ref('settings')
const dropdownRef = ref(null)
const hoverTimer = ref(null)

// 头像回退逻辑
const currentAvatarIndex = ref(0)
const currentAvatarUrl = computed(() => {
  const avatarUrls = props.user?.photoURLs || [props.user?.photoURL].filter(Boolean)

  if (!avatarUrls || avatarUrls.length === 0) {
    return null
  }

  // 如果当前索引超出范围，返回默认头像
  if (currentAvatarIndex.value >= avatarUrls.length) {
    return '/image/avator.png'
  }

  return avatarUrls[currentAvatarIndex.value]
})

// 处理头像加载错误
const handleAvatarError = () => {
  console.log('Avatar load error, trying next fallback')
  currentAvatarIndex.value++
}

// 监听用户数据变化，重置错误状态
watch(() => [props.user?.photoURL, props.user?.photoURLs], () => {
  currentAvatarIndex.value = 0
}, { deep: true })

function toggleDropdown() {
  emit('update:open', !props.open)
}

function closeDropdown() {
  emit('update:open', false)
}

function openSettingsModal(tab) {
  activeTab.value = tab
  showSettingsModal.value = true
  closeDropdown() // 关闭下拉菜单
  
  // 通知导航栏降低z-index
  $emitter.emit('settings-modal', true)
}

function closeSettingsModal() {
  showSettingsModal.value = false
  
  // 通知导航栏恢复z-index
  $emitter.emit('settings-modal', false)
}

function handleSignout() {
  emit('signout')
  closeDropdown() // 关闭下拉菜单
  closeSettingsModal() // 确保关闭设置浮窗
}

function handleClickOutside(event) {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    closeDropdown()
  }
}

function handleAvatarHover() {
  // 清除任何存在的隐藏定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value)
    hoverTimer.value = null
  }
  emit('update:open', true)
}

function handleAvatarLeave() {
  // 设置延迟隐藏，给用户时间移动到下拉菜单上
  hoverTimer.value = setTimeout(() => {
    emit('update:open', false)
  }, 150)
}

function handleDropdownEnter() {
  // 当鼠标进入下拉菜单时，清除隐藏定时器并保持显示状态
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value)
    hoverTimer.value = null
  }
  emit('update:open', true)
}

function handleDropdownLeave() {
  // 当鼠标离开下拉菜单时，立即隐藏
  emit('update:open', false)
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  
  // 清理定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value)
  }
  
  // 组件销毁时确保恢复导航栏z-index
  if (showSettingsModal.value) {
    $emitter.emit('settings-modal', false)
  }
})
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  width: 16rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  z-index: 50;
  padding: 1rem;
}

.dark .dropdown-menu {
  background: #141415;
  border: 1px solid #27282D;
}

.dropdown-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 0.5rem;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-btn:hover {
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.dark .dropdown-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
}

.dropdown-separator {
  height: 0.5px;
  background-color: #f3f4f6;
  margin: 0.25rem 0;
}

.dark .dropdown-separator {
  background-color: #27282D;
}

.user-dropdown-header {
  background-color: #f9fafb;
  border-radius: 0.5rem 0.5rem 0 0;
  width: calc(100% + 2rem);
  box-sizing: border-box;
  padding: 0.75rem 1rem;
  margin: -1rem -1rem 1rem -1rem;
}

.dark .user-dropdown-header {
  background-color: rgba(255, 255, 255, 0.05);
}

.user-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.user-username {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.settings-modal {
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}
.settings-tab-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 1rem;
  background: none;
  border: none;
  font-size: 1rem;
  color: #8D8D8D;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.settings-tab-btn.active, .settings-tab-btn:hover {
  background: #F7F8FA;
  color: #222;
}

.avatar-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #EDEDED;
  transition: box-shadow 0.2s ease, border-color 0.2s ease;
}

.avatar-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .avatar-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #3A3A3A;
}

.dark .avatar-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}
</style> 